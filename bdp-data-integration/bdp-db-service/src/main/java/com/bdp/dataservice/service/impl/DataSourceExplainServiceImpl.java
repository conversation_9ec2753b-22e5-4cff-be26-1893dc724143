package com.bdp.dataservice.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bdp.auth.dep.util.UserInfoHelper;
import com.bdp.common.dataEncrypt.AESUtil;
import com.bdp.common.dataEncrypt.Sm2Util;
import com.bdp.common.domain.Constant;
import com.bdp.common.enums.IsDeleteEnum;
import com.bdp.common.vo.DataSourceVO;
import com.bdp.common.vo.ServiceResult;
import com.bdp.data.federation.calcite.CalciteHikariDataSource;
import com.bdp.data.federation.config.DataFederationProperties;
import com.bdp.data.federation.util.DataSourceUtil;
import com.bdp.dataservice.entity.DbExecutorLogEntity;
import com.bdp.dataservice.entity.FileDirRecordEntity;
import com.bdp.dataservice.mapper.DbExecutorLogDao;
import com.bdp.dataservice.mapper.FileDirRecordDao;
import com.bdp.dataservice.service.DataSourceExplainService;
import com.bdp.dataservice.service.DataSourceService;
import com.bdp.dataservice.vo.*;
import com.google.common.collect.Lists;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertyName;
import org.springframework.boot.context.properties.source.ConfigurationPropertyNameAliases;
import org.springframework.boot.context.properties.source.ConfigurationPropertySource;
import org.springframework.boot.context.properties.source.MapConfigurationPropertySource;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

@Service
@RequiredArgsConstructor
public class DataSourceExplainServiceImpl extends ServiceImpl<FileDirRecordDao, FileDirRecordEntity> implements DataSourceExplainService {

	private final DataSourceService dataSourceService;

	private final DbExecutorLogDao dbExecutorLogDao;

	private Map<String, DataSource> dataSourceMap = new HashMap<>();

	@Override
	public ServiceResult add(FileDirRecordRequestVO requestVO) {
		FileDirRecordEntity parent = null;
		if (requestVO.getParentId() == 0) {
			 parent = new FileDirRecordEntity();
		} else {
			 parent = getById(requestVO.getParentId());
			//文件下不能有子项
			if (parent != null && parent.getType() == 1) {
				return ServiceResult.failure("文件层级异常");
			}

			if (parent.getLevel() + 1 > Constant.FILE_LEVEL_MAX) {
				return ServiceResult.failure("文件层级过深");
			}
		}

		// 限制同名文件或文件夹
		LambdaQueryWrapper<FileDirRecordEntity> wrapper = new LambdaQueryWrapper<FileDirRecordEntity>()
				.eq(FileDirRecordEntity::getName, requestVO.getName())
				.eq(FileDirRecordEntity::getIsDelete, IsDeleteEnum.EXISTED.getValue())
				.eq(FileDirRecordEntity::getId, requestVO.getParentId());
		List<FileDirRecordEntity> list = list(wrapper);
		if (list != null && !list.isEmpty()) {
			return ServiceResult.failure("命名重复");
		}

		FileDirRecordEntity record = new FileDirRecordEntity();
		record.setName(requestVO.getName());
		record.setType(requestVO.getType());
		record.setParentId(requestVO.getParentId() == null ? 0L : requestVO.getParentId());
		record.setLevel(requestVO.getParentId() == null ? 1 : parent.getLevel() + 1);
		record.setCreatedBy(UserInfoHelper.getUserInfo().getUserId());
		record.setCreatedName(UserInfoHelper.getUserInfo().getUsername());
		boolean save = save(record);
		return ServiceResult.success(save);
	}

	@Override
	public ServiceResult<Boolean> update(FileDirRecordUpdateVO requestVO) {
		LambdaQueryWrapper<FileDirRecordEntity> wrapper = new LambdaQueryWrapper<FileDirRecordEntity>()
				.eq(FileDirRecordEntity::getName, requestVO.getName())
				.ne(FileDirRecordEntity::getId, requestVO.getId())
				.eq(FileDirRecordEntity::getIsDelete, IsDeleteEnum.EXISTED.getValue());
		List<FileDirRecordEntity> list = list(wrapper);
		if (list != null && !list.isEmpty()) {
			return ServiceResult.failure("命名重复");
		}
		FileDirRecordEntity entity = getById(requestVO.getId());
		entity.setName(requestVO.getName());
		boolean update = updateById(entity);
		return ServiceResult.success(update);
	}

	@Override
	public ServiceResult<Boolean> delete(Long id) {
		FileDirRecordEntity record = getById(id);
		if (record == null) {
			return ServiceResult.failure("目标文件不存在");
		}
		record.setIsDelete(IsDeleteEnum.DELETED.getValue());
		return ServiceResult.success(updateById(record));
	}

	@Override
	public ServiceResult<List<FileDirRecordResponseVO>> listByQuery(String queryStr) {
		List<FileDirRecordResponseVO> result = baseMapper.listByQuery(queryStr);
		return ServiceResult.success(result);
	}

	@Override
	public ServiceResult<Object> saveContent(SqlSaveRequestVo vo) {
		vo.setSqlTxt(AESUtil.aesDecrypt(vo.getSqlTxt(),AESUtil.key));
		FileDirRecordEntity fileDirRecord = getById(vo.getRecordId());
		// SQL加密
		fileDirRecord.setContent(Sm2Util.encryptBase64(vo.getSqlTxt(), Sm2Util.SQL_PUBLIC_KEY));
		fileDirRecord.setDataSourceKey(vo.getDataSourceKey());
		fileDirRecord.setDataSourceTypeKey(vo.getDataSourceTypeKey());
		return ServiceResult.success(updateById(fileDirRecord));
	}

	@Override
	public ServiceResult<SqlSaveRequestVo> getContent(Long recordId) {
		SqlSaveRequestVo sqlSaveRequestVo = new SqlSaveRequestVo();
		FileDirRecordEntity byId = getById(recordId);
		String content = byId.getContent();
		if (StringUtils.isNotBlank(content)) {
			content = Sm2Util.decryptBase64(getById(recordId).getContent(), Sm2Util.SQL_PRIVATE_KEY);
		} else {
			content = "";
		}
		sqlSaveRequestVo.setSqlTxt(content);
		sqlSaveRequestVo.setDataSourceKey(byId.getDataSourceKey());
		sqlSaveRequestVo.setDataSourceTypeKey(byId.getDataSourceTypeKey());
		return ServiceResult.success(sqlSaveRequestVo);
	}

	@Override
	public ServiceResult<Object> executor(SqlExecutorRequestVo vo) throws Exception {
		vo.setSqlTxt(AESUtil.aesDecrypt(vo.getSqlTxt(),AESUtil.key));
		DbExecutorLogEntity log = DbExecutorLogEntity.builder().sqlTxt(Sm2Util.encryptBase64(vo.getSqlTxt(), Sm2Util.SQL_PUBLIC_KEY))
				.recordId(vo.getRecordId())
				.createdBy(UserInfoHelper.getUserInfo().getUserId())
				.createdName(UserInfoHelper.getUserInfo().getUsername())
				.build();
		dbExecutorLogDao.insert(log);

		if (StringUtils.isBlank(vo.getSqlTxt())) {
			return ServiceResult.success("SQL异常");
		}
		Assert.notBlank(vo.getSource(), "数据库不能为空");
		final DataSourceVO dataSourceVO = dataSourceService.getOne(vo.getSource());
		Assert.notNull(dataSourceVO, "数据库不存在：" + vo.getSource());

		String checkSql = vo.getSqlTxt().toUpperCase(Locale.ROOT);
		List<String> baseList = Lists.newArrayList("insert", "update", "delete", "drop", "truncate",
				 "alter", "grant", "revoke", "declare", "merge");

		for (String str : baseList) {
			if (checkSql.contains(str.toUpperCase(Locale.ROOT))) {
				return ServiceResult.success("SQL异常，SQL包含不被允许关键字" + str);
			}
		}

		DataSourceVO sourceVO = dataSourceService.getOne(vo.getSource());
		loadDataSource(sourceVO);

		DataSource dataSource = dataSourceMap.get(vo.getSource());

		int offset = (vo.getPage() - 1) * vo.getPageSize();

		StringBuffer sb = new StringBuffer();
		sb.append("select SQLBySource.* from (");
		sb.append(vo.getSqlTxt());
		sb.append(" ) as SQLBySource");


		StringBuffer countSb = new StringBuffer();
		countSb.append("select count(*) as counts from (");
		countSb.append(vo.getSqlTxt());
		countSb.append(" ) as SQLBySource");

		final String lastCountSql = countSb.toString();
		Connection connection = null;
		List<Map<String, Object>> list = new ArrayList<>();
		int counts = 0;
		try {
			connection = dataSource.getConnection();
			Statement countStatement = connection.createStatement();
			ResultSet countResult = countStatement.executeQuery(lastCountSql);
			if (countResult.next()) {
				counts = countResult.getInt("counts");
			}

			int limit = vo.getLimit() > Constant.DATA_SERVICE_QUERY_MAX ? Constant.DATA_SERVICE_QUERY_MAX : vo.getLimit();

			if (offset != 0 && offset > limit) {
				return ServiceResult.success(PageObjectVO.of(counts, vo.getPage(), vo.getPageSize(), list));
			}  else {
				int newLimit = Math.min(limit, vo.getPageSize());
				sb.append(" limit " + newLimit);
				sb.append(" offset " + offset);

				final String lastSql = sb.toString();
				Statement statement = connection.createStatement();
				ResultSet resultSet = statement.executeQuery(lastSql);
				ResultSetMetaData metaData = resultSet.getMetaData();
				int columnCount = metaData.getColumnCount();
				while (resultSet.next()) {
					Map<String, Object> map = new LinkedHashMap<>();
					for (int i = 1; i <= columnCount; i++) {
						map.put(metaData.getColumnName(i), resultSet.getObject(i));
					}
					list.add(map);
				}
			}

		} catch (SQLException e) {
			throw new Exception(e);
		} finally {
			if (connection != null) {
				try {
					connection.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return ServiceResult.success(PageObjectVO.of(counts, vo.getPage(), vo.getPageSize(), list));
	}

	@Override
	public List<Map<String, Object>> executeSql(SqlExportRequestVo vo) throws Exception {
		if (StringUtils.isBlank(vo.getSqlTxt())) {
			throw new Exception("SQL异常");
		}
		vo.setSqlTxt(AESUtil.aesDecrypt(vo.getSqlTxt(),AESUtil.key));

		String checkSql = vo.getSqlTxt().toUpperCase(Locale.ROOT);
		List<String> baseList = Lists.newArrayList("insert", "update", "delete", "drop", "truncate",
				"create", "alter", "grant", "revoke", "declare", "merge");

		for (String str : baseList) {
			if (checkSql.contains(str.toUpperCase(Locale.ROOT))) {
				throw new Exception("SQL异常，SQL包含不被允许关键字" + str);
			}
		}

		DataSourceVO sourceVO = dataSourceService.getOne(vo.getSource());
		loadDataSource(sourceVO);

		DataSource dataSource = dataSourceMap.get(vo.getSource());

		StringBuffer sb = new StringBuffer();
		sb.append("select SQLBySource.* from (");
		sb.append(vo.getSqlTxt());
		sb.append(" ) as SQLBySource");

		String[] strs = vo.getSource().split("\\$");

		if (strs.length == 2 && Constant.CODE.equals(strs[1])) {
			sb.append(" limit " + Constant.DATA_SERVICE_EXPORT_MAX);
		} else {
			sb.append(" limit " + vo.getLimit());
		}


		final String lastSql = sb.toString();
		Connection connection = null;
		List<Map<String, Object>> list = new ArrayList<>();
		try {
			connection = dataSource.getConnection();
			PreparedStatement statement = connection.prepareStatement(lastSql);
			ResultSet resultSet = statement.executeQuery(lastSql);
			ResultSetMetaData metaData = resultSet.getMetaData();
			int columnCount = metaData.getColumnCount();
			while (resultSet.next()) {
				Map<String, Object> map = new LinkedHashMap<>();
				for (int i = 1; i <= columnCount; i++) {
					map.put(metaData.getColumnName(i), resultSet.getObject(i));
				}
				list.add(map);
			}

		} catch (SQLException e) {
			throw new Exception(e);
		} finally {
			if (connection != null) {
				try {
					connection.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}

	private void loadDataSource(DataSourceVO sourceVO) {
		//目前暂时不支持datasource的更新和删除，只支持新增
		if (!dataSourceMap.containsKey(sourceVO.getDataSourceKey())) {
			createDataSource(sourceVO);
		}
		//添加数据联邦的数据源
		DataFederationProperties dataFederationProperties = new DataFederationProperties();
		CalciteHikariDataSource ds = DataSourceUtil.buildDataSource(dataFederationProperties);
		List<DataSource> dataSourceList = new ArrayList<>(dataSourceMap.values());
		ds.setDataSourceList(dataSourceList);
		dataSourceMap.put("ddh", ds);
	}

	private void createDataSource(DataSourceVO dataSourceVO) {
		DataSource dataSource = null;
		try {
			dataSource = new HikariDataSource();
			Map<String, Object> map = new HashMap<>();
			map.put("driverClassName", dataSourceVO.getDriverClassName());
			map.put("username", dataSourceVO.getUsername());
			map.put("password", dataSourceVO.getPassword());
			map.put("url", dataSourceVO.getUrl());
			ConfigurationPropertySource source = new MapConfigurationPropertySource(map);
			ConfigurationPropertyNameAliases aliases = new ConfigurationPropertyNameAliases();
			aliases.addAliases("url", "jdbc-url");
			aliases.addAliases("username", "user");
			Binder binder = new Binder(source.withAliases(aliases));
			binder.bind(ConfigurationPropertyName.EMPTY, Bindable.ofInstance(dataSource));
			((HikariDataSource) dataSource).getDataSourceProperties().setProperty("alias", dataSourceVO.getDataSourceKey());
		} catch (Exception e) {
			dataSource = null;
		}
		if (dataSource != null) {
			dataSourceMap.put(dataSourceVO.getDataSourceKey(), dataSource);
		}
	}
}
