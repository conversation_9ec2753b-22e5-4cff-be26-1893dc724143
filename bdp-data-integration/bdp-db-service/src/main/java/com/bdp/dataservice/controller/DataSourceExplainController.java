package com.bdp.dataservice.controller;

import com.bdp.common.util.ExcelExportUtil;
import com.bdp.common.vo.ServiceResult;
import com.bdp.dataservice.entity.FileDirRecordEntity;
import com.bdp.dataservice.service.DataSourceExplainService;
import com.bdp.dataservice.service.DbExecutorLogService;
import com.bdp.dataservice.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Api(value = "自助分析", tags = {"自助分析"})
@RestController
@RequiredArgsConstructor
@CrossOrigin
public class DataSourceExplainController {

	private final DataSourceExplainService dataSourceExplainService;

	private final DbExecutorLogService dbExecutorLogService;

	@ApiOperation(value = "新增文件夹/文件", notes = "新增文件夹/文件", response = Boolean.class)
	@PostMapping("/dataExplain/add")
	@ResponseBody
	public ServiceResult<Boolean> add(@Validated @RequestBody FileDirRecordRequestVO requestVO) {
		return dataSourceExplainService.add(requestVO);
	}

	@ApiOperation(value = "修改文件夹/文件名字", notes = "修改文件夹/文件名字", response = Boolean.class)
	@PostMapping("/dataExplain/update")
	@ResponseBody
	public ServiceResult<Boolean> update(@Validated @RequestBody FileDirRecordUpdateVO requestVO) {
		return dataSourceExplainService.update(requestVO);
	}

	@ApiOperation(value = "删除文件夹/文件", notes = "删除文件夹/文件", response = Boolean.class)
	@GetMapping("/dataExplain/delete")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "文件id", paramType = "query", required = true),
	})
	@ResponseBody
	public ServiceResult<Boolean> delete(@RequestParam("id") Long id) {
		return dataSourceExplainService.delete(id);
	}

	@ApiOperation(value = "获取所有目录", notes = "获取所有目录", response = FileDirRecordResponseVO.class)
	@GetMapping("/dataExplain/list")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "queryStr", value = "文件name", paramType = "query"),
	})
	@ResponseBody
	public ServiceResult<List<FileDirRecordResponseVO>> listByQuery(String queryStr) {
		return dataSourceExplainService.listByQuery(queryStr);
	}

	@ApiOperation(value = "SQL执行", notes = "SQL执行", response = Object.class)
	@PostMapping("/dataExplain/executor")
	@ResponseBody
	public ServiceResult<Object> executor(@RequestBody SqlExecutorRequestVo vo) {
		try {
			ServiceResult<Object> result = dataSourceExplainService.executor(vo);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			return ServiceResult.success(e.getMessage());
		}
	}

	@ApiOperation(value = "保存", notes = "保存", response = Boolean.class)
	@PostMapping("/dataExplain/saveContent")
	@ResponseBody
	public ServiceResult saveContent(@RequestBody SqlSaveRequestVo vo) {
		return dataSourceExplainService.saveContent(vo);
	}

	@ApiOperation(value = "获取文件内容", notes = "获取文件内容", response = String.class)
	@GetMapping("/dataExplain/getContent")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "recordId", value = "文件id", paramType = "query", required = true),
	})
	@ResponseBody
	public ServiceResult<SqlSaveRequestVo> getContent(@RequestParam("recordId") Long recordId) {
		return dataSourceExplainService.getContent(recordId);
	}

	@ApiOperation(value = "获取执行历史", notes = "获取执行历史", response = DbExecutorLogResponseVO.class)
	@GetMapping("/dataExplain/history")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "recordId", value = "文件id", paramType = "query", required = true),
	})
	@ResponseBody
	public ServiceResult<List<DbExecutorLogResponseVO>> listHistory(@RequestParam("recordId") Long recordId) {
		return dbExecutorLogService.listHistory(recordId);
	}

	@ApiOperation(value = "数据导出", response = Map.class)
	@PostMapping("/dataExplain/export")
	public void export(@RequestBody SqlExportRequestVo vo, HttpServletResponse response) throws Exception {
		List<Map<String, Object>> result = dataSourceExplainService.executeSql(vo);

		List<List<Object>> dataList = new ArrayList<>();
		List<Object> shellList = new ArrayList<>();

		if (result != null && !result.isEmpty()) {
			Map<String, Object> keys = result.get(0);
			for (Map.Entry<String, Object> entry : keys.entrySet()) {
				shellList.add(entry.getKey());
			}
			dataList.add(shellList);
			for (Map<String, Object> objectMap : result) {
				shellList = new ArrayList<>();
				for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
					String value = String.valueOf(entry.getValue());
					shellList.add(value);
				}
				dataList.add(shellList);
			}
			FileDirRecordEntity fileDirRecord = dataSourceExplainService.getById(vo.getRecordId());
			String title = "export";
			String fileName = String.valueOf(System.currentTimeMillis());
			// 调用工具类进行导出
			ExcelExportUtil.easySheet(fileName, title, dataList, response);
//			ExcelExportUtil.easySheetHasTital(fileName, title, dataList, title, response);
		} else {
			return;
		}
	}
}
