package com.bdp.dataservice.util;

import com.bdp.common.exception.BizException;
import org.apache.calcite.config.Lex;
import org.apache.calcite.rel.type.RelDataTypeFactory;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlBasicVisitor;
import org.apache.calcite.sql.validate.SqlValidatorCatalogReader;
import org.apache.calcite.sql.validate.SqlValidatorImpl;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SqlValidator2 {

    public static boolean validateSqlSafeQuery(String sql, String dataSourceType) {
        // 创建 Calcite SQL 解析器
        SqlParser.Config parserConfig = SqlParser.config()
                .withLex(Lex.MYSQL) // 按需切换方言
                .withIdentifierMaxLength(256); // 防止超长标识符攻击
        SqlParser parser = SqlParser.create(sql, parserConfig);
        final SqlNode sqlNode;
        try {
            sqlNode = parser.parseStmt();
            sqlNode.accept(new SecurityChecker());
            if (parser.parseStmt().getKind().belongsTo(SqlKind.QUERY)) {
                throw new BizException("sql异常，非查询语句");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return false;
    }


    // 在 SQL 校验阶段检测敏感字段
    static class SafeValidator extends SqlValidatorImpl {
        protected SafeValidator(SqlOperatorTable opTab, SqlValidatorCatalogReader catalogReader, RelDataTypeFactory typeFactory, Config config) {
            super(opTab, catalogReader, typeFactory, config);
        }
        private final List<String> sensitiveColumns = Arrays.asList("password", "credit_card");

        @Override
        public SqlNode validate(SqlNode node) {
            return super.validate(node);
        }

        // @Override
        // public void validate(SqlSelect select, SqlIdentifier alias) {
        //     String columnName = alias.getSimple();
        //     if (sensitiveColumns.contains(columnName.toLowerCase())) {
        //         throw new SecurityException("无权访问敏感字段: " + columnName);
        //     }
        //     super.validate(select, alias);
        // }
    }

    // 自定义安全检测 Visitor
    static class SecurityChecker extends SqlBasicVisitor<Void> {
        @Override
        public Void visit(SqlCall call) {
            // 检测多语句执行（如 '; DROP TABLE'）
            if (call.getKind() == SqlKind.OTHER) {
                throw new SecurityException("检测到多语句执行风险");
            }
            checkDangerousFunctions(call);
            return super.visit(call);
        }
        private void checkDangerousFunctions(SqlNode sqlNode) {
            if (sqlNode instanceof SqlCall) {
                SqlCall call = (SqlCall) sqlNode;
                if (call.getOperator() instanceof SqlFunction) {
                    String funcName = call.getOperator().getName();
                    if ("LOAD_FILE".equalsIgnoreCase(funcName)) {
                        throw new SecurityException("禁止使用高危函数: " + funcName);
                    }
                }
                // 递归检查子节点
                for (SqlNode operand : call.getOperandList()) {
                    if (operand != null) {
                        checkDangerousFunctions(operand);
                    }
                }
            }
        }
    }

}
