/*
 * Copyright (c) 2019 The StreamX Project
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bdp.streamx.console;

import com.bdp.streamx.console.base.config.SpringProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.streampark.common.util.SystemPropertyUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.net.InetAddress;
import java.util.Optional;
import java.util.Properties;

@Slf4j
@CrossOrigin
@SpringBootApplication(scanBasePackages = "com.bdp")
@EnableScheduling
@EnableFeignClients(basePackages = {"com.bdp.auth.dep.rpc"})
@MapperScan(basePackages = {"com.bdp.common", "com.bdp.streamx.console.core.mapper"})
public class StreamxrealApplication {

    //	@Value("${streampark.workspace.home}")

    public static void main(String[] args) {
        try {

            for (int i = 0; i < args.length; i++) {
                log.info("StreamxrealApplication arg[{}]: {}", i + 1, args[i]);
            }
            String activeProfile;
            if (args.length == 1) {
                activeProfile = args[0].split("=")[1];
            }else{
                // 获取 spring.profiles.active 的值
                activeProfile = Optional.ofNullable(System.getProperty("spring.profiles.active"))
                        .orElseGet(() -> System.getenv("SPRING_PROFILES_ACTIVE"));
            }

            String configFileName = "application-%s.yml";
            if (activeProfile != null) {
                log.info("Active profile: {}", activeProfile);
                configFileName = String.format(configFileName, activeProfile);
            } else {
                configFileName = "application-test.yml";
                log.error("No active profile specified.");
            }

            File file = ClasspathFileReader.getClasspathFile(configFileName);

            ConfigurableApplicationContext context = new SpringApplicationBuilder()
                    .properties(SpringProperties.get(file.getAbsolutePath()))
                    .sources(StreamxrealApplication.class)
                    .run(args);
            String pid = SystemPropertyUtils.get("pid");
            if (pid != null) {
                context.addApplicationListener(new ApplicationPidFileWriter());
            }
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("application shutdown now, pid: " + getPid());
                if (pid != null) {
                    File pidFile = new File(pid);
                    pidFile.delete();
                }
            }));


            Environment env = context.getEnvironment();

            log.info("\n----------------------------------------------------------\n\t" +
                            "应用 '{}' 运行成功! \n\t" +
                            "Swagger文档: \t\thttp://{}:{}{}/swagger-ui.html\n\t" +
                            "----------------------------------------------------------",
                    env.getProperty("spring.application.name"),
                    InetAddress.getLocalHost().getHostAddress(),
                    env.getProperty("server.port"),
                    env.getProperty("server.servlet.context-path"));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Integer getPid() {
        RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
        String name = runtime.getName();
        try {
            return Integer.parseInt(name.substring(0, name.indexOf('@')));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

}
