server:
  port: 18000
  #Undertow是一个灵活的Web服务器和Servlet容器
  undertow:
    #服务器使用的缓冲区大小
    buffer-size: 1024
    #是否使用直接缓冲区。直接缓冲区直接分配在操作系统的本地内存中
    direct-buffers: true
    threads:
      #处理I/O操作的线程数
      io: 4
      #处理HTTP请求的工作线程数
      worker: 20
#log 日志输出级别
logging:
  level:
    root: info
#    com.baomidou.mybatisplus.core.mapper: DEBUG

spring:
  devtools:
    restart:
      #关闭Spring Boot开发者工具的自动重启功能
      enabled: false
  servlet:
    multipart:
      #启用文件上传
      enabled: true
      #设置单个文件上传的最大大小。
      max-file-size: 500MB
      #设置整个请求的最大大小。
      max-request-size: 500MB
  datasource:
    dynamic:
      # 是否开启 SQL日志输出，生产环境建议关闭，有性能损耗
      p6spy: false
      #设置HikariCP连接池的属性
      hikari:
        #连接超时时间
        connection-timeout: 30000
        #连接的最大生存时间
        max-lifetime: 1800000
        #连接池的最大大小
        max-pool-size: 15
        #连接池中的最小空闲连接数
        min-idle: 5
        #用于测试连接的SQL查询
        connection-test-query: select 1
        #连接池的名称
        pool-name: HikariCP-DS-POOL
      # 配置默认数据源
      primary: primary
      datasource:
        # 数据源-1，名称为 primary
        primary:
          username: root
          password: 1qaz@WSX
          driver-class-name: com.mysql.jdbc.Driver
          #          url: ***********************************************************************************************************************************************************************************************************
          url: ***************************************************************************************************************************************************************************************************************
  #使用基于类的代理，而不是基于接口的代理。这对于没有接口的类特别有用。
  aop.proxy-target-class: true
  #设置消息源（通常是国际化属性文件）的编码为utf-8。
  messages.encoding: utf-8
  #设置Jackson库用于JSON序列化和反序列化的属性：
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  #关闭Spring Boot启动时的横幅（banner）。当你不想在控制台看到启动时的ASCII艺术时，可以关闭它。
  main:
    banner-mode: off
#这是Spring Boot Actuator的顶级配置前缀，用于定义与应用程序管理相关的设置
management:
  #定义了如何公开和配置Actuator端点
  endpoints:
    #这些设置与Web端点有关，特别是当Actuator端点通过Web暴露时。
    web:
      #控制哪些端点应该被暴露。
      exposure:
        #一个列表，包含了应该被暴露的端点ID。在这个例子中，httptrace 和 metrics 端点被暴露。
        #httptrace: 这个端点提供HTTP请求和响应的跟踪信息。这对于调试和监视应用程序与HTTP客户端之间的交互非常有用。
        #metrics: 这个端点提供应用程序的各种度量信息，如JVM内存使用情况、GC次数、线程状态等。这些信息对于性能调优和故障排查非常有帮助。
        include: [ 'httptrace', 'metrics' ]

#mybatis plus 设置  com.streamxhub.streamx.console.*.entity
mybatis-plus:
  type-aliases-package: com.bdp.streamx.console.*.entity
  mapper-locations: classpath:mapper/*/*.xml
  configuration:
    jdbc-type-for-null: null
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
  global-config:
    db-config:
      id-type: auto
    # 关闭 mybatis-plus的 banner
    banner: false
datasource:
  dialect: mysql

hadoop:
  xml:
    url: /data/streamx_console/upload_cnf/

streampark:
  # HADOOP_USER_NAME
  hadoop-user-name: root
  # 本地的工作空间,用于存放项目源码,构建的目录等.
  workspace:
    local: /data/streamx_console/streamx_workspace
    home: /data/streamx_console/streamx-console-service-2.1.4/
    remote: /streampark

bdp:
  auth:
    service:
      url: *************:19870