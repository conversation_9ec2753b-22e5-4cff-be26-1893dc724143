/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_vfs

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:33:18
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_file
-- ----------------------------
DROP TABLE IF EXISTS `t_file`;
CREATE TABLE `t_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件id',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件名称',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '所属文件夹',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `creator` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `uid` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户标识',
  `file_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件类型',
  `object_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=197 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='文件';

-- ----------------------------
-- Table structure for t_file_tags
-- ----------------------------
DROP TABLE IF EXISTS `t_file_tags`;
CREATE TABLE `t_file_tags` (
  `file_id` bigint(20) NOT NULL,
  `tag_id` bigint(20) NOT NULL,
  PRIMARY KEY (`file_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_folder
-- ----------------------------
DROP TABLE IF EXISTS `t_folder`;
CREATE TABLE `t_folder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父级ID',
  `name` varchar(255) DEFAULT NULL COMMENT '目录名称',
  `removed` tinyint(1) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `absolute_path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='目录表';

-- ----------------------------
-- Table structure for t_tags
-- ----------------------------
DROP TABLE IF EXISTS `t_tags`;
CREATE TABLE `t_tags` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标签名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

SET FOREIGN_KEY_CHECKS = 1;


-- ----------------------------
    -- init data

INSERT INTO `t_folder` (`id`, `parent_id`, `name`, `removed`, `create_time`, `update_time`, `absolute_path`) VALUES (1, 0, 'public', 0, NULL, NULL, '/public');