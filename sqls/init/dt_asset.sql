/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_asset

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:32:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_api_favorite
-- ----------------------------
DROP TABLE IF EXISTS `t_api_favorite`;
CREATE TABLE `t_api_favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id主键',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `api_id` bigint(20) DEFAULT NULL COMMENT 'apiid',
  `name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'api名称',
  `url` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'api的url',
  `project_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '项目名称',
  `creator` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `method` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求方法',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_application
-- ----------------------------
DROP TABLE IF EXISTS `t_application`;
CREATE TABLE `t_application` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '应用名称',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '应用描述',
  `create_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `ip_list` text COLLATE utf8mb4_bin COMMENT 'ip列表',
  `app_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '枚举类型',
  `app_secret` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'app secret',
  `app_id` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'app id',
  `app_key` text COLLATE utf8mb4_bin COMMENT 'app_key',
  `pass_key` text COLLATE utf8mb4_bin COMMENT 'pass_key',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_application_related
-- ----------------------------
DROP TABLE IF EXISTS `t_application_related`;
CREATE TABLE `t_application_related` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `application_id` bigint(11) DEFAULT NULL,
  `asset_id` bigint(11) DEFAULT NULL,
  `status` varchar(6) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'LOCK,UNLOCK',
  `related_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `related_time` datetime DEFAULT NULL,
  `create_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `update_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `start_time` date DEFAULT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `asset_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '资产类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_asset_folder
-- ----------------------------
DROP TABLE IF EXISTS `t_asset_folder`;
CREATE TABLE `t_asset_folder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父目录id',
  `name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目录名称',
  `asset_type` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_folder_api
-- ----------------------------
DROP TABLE IF EXISTS `t_folder_api`;
CREATE TABLE `t_folder_api` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `api_id` bigint(20) DEFAULT NULL COMMENT 'apiid',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '目录id',
  `project_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '项目名称',
  `api_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'api名称',
  `api_desc` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接口描述',
  `method` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求方法',
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'url',
  `deployed` int(1) DEFAULT '1' COMMENT '是否部署',
  `removed` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `apps` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '授权应用',
  `mount_time` datetime DEFAULT NULL COMMENT '挂载时间',
  `creator` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '挂载人',
  `creator_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=160 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_folder_dashboard
-- ----------------------------
DROP TABLE IF EXISTS `t_folder_dashboard`;
CREATE TABLE `t_folder_dashboard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `dashboard_id` bigint(20) DEFAULT NULL COMMENT '大屏id',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '目录id',
  `dashboard_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '大屏名称',
  `dashboard_desc` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '大屏描述',
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'url',
  `deployed` int(1) DEFAULT '1' COMMENT '是否部署',
  `removed` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `mount_time` datetime DEFAULT NULL COMMENT '挂载时间',
  `creator` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '挂载人',
  `creator_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_folder_report
-- ----------------------------
DROP TABLE IF EXISTS `t_folder_report`;
CREATE TABLE `t_folder_report` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_id` bigint(20) DEFAULT NULL COMMENT '报表ID',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '文件夹ID',
  `mount_time` datetime DEFAULT NULL COMMENT '挂载时间',
  `report_name` varchar(255) DEFAULT NULL COMMENT '报表名称',
  `report_remark` varchar(255) DEFAULT NULL COMMENT '报表备注',
  `deployed` tinyint(1) DEFAULT NULL COMMENT '是否发布',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version_num` int(11) DEFAULT '0' COMMENT '版本号',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(64) DEFAULT NULL COMMENT '修改人',
  `creator_id` bigint(20) DEFAULT NULL,
  `modifier_id` bigint(20) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `removed` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COMMENT='文件夹报表关联表';

-- ----------------------------
-- Table structure for t_folder_standard
-- ----------------------------
DROP TABLE IF EXISTS `t_folder_standard`;
CREATE TABLE `t_folder_standard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `standard_id` bigint(20) DEFAULT NULL COMMENT 'standard_id',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '目录id',
  `standard_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'standard_name',
  `standard_desc` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接口描述',
  `method` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求方法',
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'url',
  `deployed` int(1) DEFAULT '1' COMMENT '是否部署',
  `removed` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `apps` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '授权应用',
  `mount_time` datetime DEFAULT NULL COMMENT '挂载时间',
  `creator` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '挂载人',
  `creator_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_folder_table
-- ----------------------------
DROP TABLE IF EXISTS `t_folder_table`;
CREATE TABLE `t_folder_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `table_id` bigint(20) DEFAULT NULL COMMENT '表id',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '目录id',
  `mount_time` datetime DEFAULT NULL COMMENT '挂载时间',
  `creator` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '挂载人',
  `creator_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `folder_table_idx` (`table_id`,`folder_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_meta_data_source
-- ----------------------------
DROP TABLE IF EXISTS `t_meta_data_source`;
CREATE TABLE `t_meta_data_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源名称',
  `type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源类型',
  `creator` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `data_source_key` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源唯一key',
  `icon` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图标',
  `creator_name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者名称',
  `db_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据库名称',
  `user_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '连接用户名',
  `invalid` tinyint(1) DEFAULT '0' COMMENT '失效标记，true为已失效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_meta_field
-- ----------------------------
DROP TABLE IF EXISTS `t_meta_field`;
CREATE TABLE `t_meta_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `table_id` bigint(20) DEFAULT NULL COMMENT '表id',
  `name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字段名称',
  `field_desc` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字段描述',
  `field_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '字段类型',
  `required` tinyint(1) DEFAULT NULL COMMENT '是否必须',
  `is_pk` tinyint(1) DEFAULT NULL COMMENT '是否主键',
  `field_index` int(20) DEFAULT NULL COMMENT '索引',
  `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '失效标记，true为已失效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1950 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_meta_partition
-- ----------------------------
DROP TABLE IF EXISTS `t_meta_partition`;
CREATE TABLE `t_meta_partition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `data_size` bigint(20) DEFAULT NULL COMMENT '数据大小',
  `table_id` bigint(20) DEFAULT NULL COMMENT '表id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_meta_table
-- ----------------------------
DROP TABLE IF EXISTS `t_meta_table`;
CREATE TABLE `t_meta_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `table_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表名',
  `table_cn_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表中文名',
  `table_desc` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表描述',
  `data_source_id` bigint(20) DEFAULT NULL COMMENT '数据源',
  `visable` tinyint(1) DEFAULT NULL COMMENT '是否可见',
  `owner` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '负责人',
  `biz_system` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所属系统',
  `ddl` text COLLATE utf8mb4_bin COMMENT 'ddl',
  `dml` text COLLATE utf8mb4_bin COMMENT 'dml',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `table_status` varchar(10) COLLATE utf8mb4_bin DEFAULT 'IN_POGRESS' COMMENT '完成状态',
  `extra_info_list` text COLLATE utf8mb4_bin COMMENT '额外字段下拉',
  `extra_info_input` text COLLATE utf8mb4_bin COMMENT '额外字段input',
  `owner_id` bigint(20) DEFAULT NULL COMMENT '负责人id',
  `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '失效标记，true为已失效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni` (`table_name`,`data_source_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1884 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_table_favorite
-- ----------------------------
DROP TABLE IF EXISTS `t_table_favorite`;
CREATE TABLE `t_table_favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `asset_id` bigint(20) DEFAULT NULL COMMENT '表id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `fav_unique` (`user_id`,`asset_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPRESSED;

-- ----------------------------
-- Table structure for t_table_view_event
-- ----------------------------
DROP TABLE IF EXISTS `t_table_view_event`;
CREATE TABLE `t_table_view_event` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_id` bigint(20) DEFAULT NULL COMMENT '表id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `event_time` datetime DEFAULT NULL COMMENT '点击时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=171 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

SET FOREIGN_KEY_CHECKS = 1;
