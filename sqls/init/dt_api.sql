/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_api

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:32:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for api_interface_doc
-- ----------------------------
DROP TABLE IF EXISTS `api_interface_doc`;
CREATE TABLE `api_interface_doc` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文档id',
  `api_id` int(11) NOT NULL,
  `headers` text COLLATE utf8mb4_bin COMMENT '文档请求头',
  `params` text COLLATE utf8mb4_bin COMMENT '文档请请求参数',
  `body` text COLLATE utf8mb4_bin COMMENT '文档请求体',
  `response_body` longtext COLLATE utf8mb4_bin COMMENT '响应参数',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `method` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '方法',
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '地址',
  `deployed` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `body_example` text COLLATE utf8mb4_bin,
  `response_body_example` longtext COLLATE utf8mb4_bin,
  PRIMARY KEY (`id`,`api_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for api_version_history
-- ----------------------------
DROP TABLE IF EXISTS `api_version_history`;
CREATE TABLE `api_version_history` (
  `id` int(32) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `version` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '版本号',
  `flag` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'now/past',
  `time` datetime NOT NULL COMMENT '版本更新时间',
  `content` text COLLATE utf8mb4_bin,
  `operator` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `api_id` int(32) DEFAULT NULL COMMENT 'api的id',
  `param` text COLLATE utf8mb4_bin,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2902 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for api_version_history_copy1
-- ----------------------------
DROP TABLE IF EXISTS `api_version_history_copy1`;
CREATE TABLE `api_version_history_copy1` (
  `id` int(32) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `version` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '版本号',
  `flag` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'now/past',
  `time` datetime NOT NULL COMMENT '版本更新时间',
  `content` text COLLATE utf8mb4_bin,
  `operator` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `api_id` int(32) DEFAULT NULL COMMENT 'api的id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for dynamic_api
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_api`;
CREATE TABLE `dynamic_api` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `folder_id` bigint(20) NOT NULL COMMENT '所属目录',
  `content` text COLLATE utf8mb4_bin COMMENT '脚本内容',
  `description` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `method` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求方法GET/POST',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `param` text COLLATE utf8mb4_bin COMMENT '参数，JSON格式',
  `path` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '请求路径即url',
  `deployed` int(1) DEFAULT NULL COMMENT '是否发布',
  `invalid` tinyint(1) DEFAULT '0' COMMENT '是否包含错误，false没错，true有错误',
  `removed` int(1) NOT NULL COMMENT '移除标记',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `lineage_stat_time` datetime DEFAULT NULL COMMENT '血缘解析时间',
  `creator` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'LOCK/UNLOCK',
  `response_body` longtext COLLATE utf8mb4_bin COMMENT '测试响应体',
  `mounted` tinyint(1) DEFAULT '0' COMMENT '是否挂载',
  `federation` tinyint(1) DEFAULT '0' COMMENT '是否联邦查询',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=471 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for dynamic_function
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_function`;
CREATE TABLE `dynamic_function` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '函数名称',
  `folder_id` int(20) DEFAULT NULL COMMENT '文件夹id',
  `content` text COLLATE utf8mb4_bin COMMENT '函数脚本',
  `description` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `removed` bit(1) NOT NULL COMMENT '删除标记',
  `mapping_path` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路径',
  `parameters` text COLLATE utf8mb4_bin COMMENT '参数',
  `return_type` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '返回值类型',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'UNLOCK/LOCK',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for folder
-- ----------------------------
DROP TABLE IF EXISTS `folder`;
CREATE TABLE `folder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件夹名称',
  `removed` tinyint(1) DEFAULT NULL COMMENT '删除标记',
  `project_id` bigint(10) NOT NULL COMMENT '项目id',
  `path` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路径',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=134 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for function_folder
-- ----------------------------
DROP TABLE IF EXISTS `function_folder`;
CREATE TABLE `function_folder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件夹名称',
  `removed` tinyint(1) DEFAULT NULL COMMENT '删除标记',
  `project_id` bigint(10) NOT NULL COMMENT '项目id',
  `path` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路径',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for function_version_history
-- ----------------------------
DROP TABLE IF EXISTS `function_version_history`;
CREATE TABLE `function_version_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `version` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '版本号',
  `flag` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'now/past',
  `time` datetime NOT NULL COMMENT '版本更新时间',
  `content` text COLLATE utf8mb4_bin,
  `operator` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `function_id` int(11) DEFAULT NULL COMMENT 'function的id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=519 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for project
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `description` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '项目名称',
  `path` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路径',
  `removed` bit(1) NOT NULL COMMENT '删除标记',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=154 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_application
-- ----------------------------
DROP TABLE IF EXISTS `t_application`;
CREATE TABLE `t_application` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_key` text COLLATE utf8mb4_bin COMMENT '外部应用key',
  `app_secret` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '外部应用密钥',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '应用名称',
  `des` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '应用描述',
  `create_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `app_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `rsa_public_key` text COLLATE utf8mb4_bin COMMENT 'rsa公钥',
  `rsa_private_key` text COLLATE utf8mb4_bin COMMENT 'rsa私玥',
  `pass_key` text COLLATE utf8mb4_bin COMMENT '应用访问通行key',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_application_related
-- ----------------------------
DROP TABLE IF EXISTS `t_application_related`;
CREATE TABLE `t_application_related` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `application_id` bigint(11) DEFAULT NULL,
  `asset_id` bigint(11) DEFAULT NULL,
  `status` varchar(6) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'LOCK,UNLOCK',
  `related_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `related_time` datetime DEFAULT NULL,
  `create_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `update_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `start_time` date DEFAULT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `asset_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '资产类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_application_related_api
-- ----------------------------
DROP TABLE IF EXISTS `t_application_related_api`;
CREATE TABLE `t_application_related_api` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `application_id` int(11) DEFAULT NULL,
  `api_id` int(11) DEFAULT NULL,
  `status` varchar(6) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'LOCK,UNLOCK',
  `related_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `related_time` datetime DEFAULT NULL,
  `create_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `update_user` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `project_id` int(11) DEFAULT NULL COMMENT '项目id',
  `valid_time` text COLLATE utf8mb4_bin COMMENT '有效时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_kafka_consumer_info
-- ----------------------------
DROP TABLE IF EXISTS `t_kafka_consumer_info`;
CREATE TABLE `t_kafka_consumer_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `host` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '主机名',
  `topic` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '主题',
  `group_name` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '消费者组名',
  `check_date` datetime NOT NULL COMMENT '检测时间',
  `exec_time` bigint(20) NOT NULL COMMENT '运行时长',
  `messages_behind` bigint(20) NOT NULL COMMENT '待消费消息数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_kafka_topic_info
-- ----------------------------
DROP TABLE IF EXISTS `t_kafka_topic_info`;
CREATE TABLE `t_kafka_topic_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `host` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '主机名',
  `topic` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '主题',
  `max_record_date` datetime DEFAULT NULL COMMENT '最大记录时间',
  `check_date` datetime NOT NULL COMMENT '检测时间',
  `exec_time` bigint(20) NOT NULL COMMENT '运行时长',
  `max_partition_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '最大分区名称',
  `partition_count` int(11) NOT NULL COMMENT 'partition数量',
  `max_offset` bigint(20) NOT NULL COMMENT '最大offset',
  `delay` bigint(20) DEFAULT NULL COMMENT '延迟时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for user_setting
-- ----------------------------
DROP TABLE IF EXISTS `user_setting`;
CREATE TABLE `user_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `setting_key` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '设置的key',
  `setting_value` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '设置的值',
  PRIMARY KEY (`id`),
  KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

SET FOREIGN_KEY_CHECKS = 1;
