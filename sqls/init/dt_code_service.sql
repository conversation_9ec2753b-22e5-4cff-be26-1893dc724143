/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_code_service

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:32:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_mns_point
-- ----------------------------
DROP TABLE IF EXISTS `t_mns_point`;
CREATE TABLE `t_mns_point` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `id_org` varchar(50) DEFAULT NULL COMMENT '原始点号',
  `pmis_code` varchar(100) NOT NULL COMMENT 'pmis编码',
  `point_name` varchar(100) DEFAULT NULL COMMENT '点号名称',
  `name_org` varchar(100) DEFAULT NULL COMMENT '原始名称',
  `org_code` varchar(20) DEFAULT NULL COMMENT '组织编码',
  `ppis_code` varchar(50) DEFAULT NULL COMMENT 'ppis编码',
  `site_code` varchar(20) DEFAULT NULL COMMENT '站点编码',
  `site_name` varchar(50) DEFAULT NULL COMMENT '站点名称',
  `main_system_code` varchar(20) DEFAULT NULL COMMENT '主系统编码',
  `main_system_name` varchar(50) DEFAULT NULL COMMENT '主系统标识',
  `sub_system_code` varchar(20) DEFAULT NULL COMMENT '子系统编码',
  `sub_system_name` varchar(50) DEFAULT NULL COMMENT '子系统名称',
  `unit_code` varchar(20) DEFAULT NULL COMMENT '小单元编码',
  `unit_name` varchar(50) DEFAULT NULL COMMENT '小单元标识',
  `components_code` varchar(20) DEFAULT NULL COMMENT '元器件编码',
  `components_name` varchar(50) DEFAULT NULL COMMENT '元器件标识',
  `monitor_code` varchar(20) DEFAULT NULL COMMENT '监测名称代码',
  `monitor_name` varchar(50) DEFAULT NULL COMMENT '监测名称',
  `ext_serialnum` varchar(20) DEFAULT NULL COMMENT '扩展属性_测点序号',
  `ext_source` varchar(20) DEFAULT NULL COMMENT '扩展属性_数据源',
  `ext_mon_type` varchar(5) DEFAULT NULL COMMENT '扩展属性_监测类型',
  `ext_data_type` varchar(5) DEFAULT NULL COMMENT '扩展属性_数据属性',
  `editor` varchar(50) DEFAULT NULL COMMENT '编辑人员',
  `load_date` varchar(50) DEFAULT NULL COMMENT '录入时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `is_imported` varchar(5) DEFAULT NULL COMMENT '是否入库',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=817 DEFAULT CHARSET=utf8mb4 COMMENT='点码表';

SET FOREIGN_KEY_CHECKS = 1;
