/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : data_service

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:31:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for data_source
-- ----------------------------
DROP TABLE IF EXISTS `data_source`;
CREATE TABLE `data_source` (
  `data_source_key` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `driver_class_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `max_rows` int(11) NOT NULL,
  `pass` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `username` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `removed` bit(1) NOT NULL,
  `data_source_type` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL,
  `host` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '主机地址',
  `port` int(11) DEFAULT NULL COMMENT '端口',
  `database_name` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据库',
  `args` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联邦参数',
  `other_args` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '其他参数',
  `connect_status` int(11) DEFAULT '0' COMMENT '联通测试状态\n0:未联通/1:已联通',
  `connect_time` datetime DEFAULT NULL,
  `service_type` int(11) DEFAULT NULL COMMENT '服务类型',
  `service_module` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务模式',
  `http_port` int(11) DEFAULT NULL COMMENT 'httpServer端口(Doris)',
  `mysql_port` int(11) DEFAULT NULL COMMENT 'MySqlServer端口(Doris)',
  `jdbc` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JDBC(MariaDB、StarRocks)',
  `create_by` bigint(20) DEFAULT NULL,
  `create_name` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`data_source_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for data_source_type
-- ----------------------------
DROP TABLE IF EXISTS `data_source_type`;
CREATE TABLE `data_source_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `data_source_type_key` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL,
  `name` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL,
  `type` int(11) DEFAULT NULL COMMENT '0:关系型数据库/1:大数据存储/2:半结构化存储/3:Nosql/4:消息队列',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` int(11) DEFAULT '0' COMMENT '0:生效/1:失效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `data_source_type_data_source_type_key_uindex` (`data_source_type_key`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据源类型表';

-- ----------------------------
-- Table structure for db_driver
-- ----------------------------
DROP TABLE IF EXISTS `db_driver`;
CREATE TABLE `db_driver` (
  `id` bigint(20) NOT NULL,
  `class_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'driver的class名称',
  `file_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'jar包名称',
  `is_used` bit(1) NOT NULL COMMENT '是否被引用',
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `version` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本号',
  `logo_image` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for db_executor_log
-- ----------------------------
DROP TABLE IF EXISTS `db_executor_log`;
CREATE TABLE `db_executor_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint(20) DEFAULT NULL,
  `record_id` bigint(20) DEFAULT NULL COMMENT '归属文件',
  `session_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '临时文件id存储',
  `created_name` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL,
  `sql_txt` text COLLATE utf8mb4_bin,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=772 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for file_dir_record
-- ----------------------------
DROP TABLE IF EXISTS `file_dir_record`;
CREATE TABLE `file_dir_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL COMMENT '0:文件夹/1:文件',
  `name` varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '名字',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父级ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint(20) DEFAULT NULL,
  `created_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL,
  `is_delete` int(11) NOT NULL DEFAULT '0',
  `content` text COLLATE utf8mb4_bin,
  `level` int(11) DEFAULT '1' COMMENT '文件夹层级',
  `data_source_type_key` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `data_source_key` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='自助分析-左侧文件夹';

-- ----------------------------
-- Table structure for test_data
-- ----------------------------
DROP TABLE IF EXISTS `test_data`;
CREATE TABLE `test_data` (
  `id` bigint(20) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS = 1;
