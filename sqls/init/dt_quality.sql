/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_quality

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:32:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_monitor_favorite
-- ----------------------------
DROP TABLE IF EXISTS `t_monitor_favorite`;
CREATE TABLE `t_monitor_favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_id` bigint(20) NOT NULL COMMENT '监控任务id',
  `username` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  PRIMARY KEY (`id`,`task_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控任务-我的收藏';

-- ----------------------------
-- Table structure for t_monitor_group
-- ----------------------------
DROP TABLE IF EXISTS `t_monitor_group`;
CREATE TABLE `t_monitor_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分组名称',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `level` int(11) DEFAULT NULL COMMENT '层级',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '层级路径',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='质量监控-分组表';

-- ----------------------------
-- Table structure for t_monitor_strategy
-- ----------------------------
DROP TABLE IF EXISTS `t_monitor_strategy`;
CREATE TABLE `t_monitor_strategy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `rule_id` bigint(20) NOT NULL COMMENT '规则id',
  `rule_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'table->单表监控，define->定义监控',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '策略名称',
  `des` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '策略描述',
  `fields` text COLLATE utf8mb4_unicode_ci COMMENT '监控字段',
  `aggregation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '聚合操作',
  `filters` longtext COLLATE utf8mb4_unicode_ci COMMENT '过滤条件',
  `triggers` longtext COLLATE utf8mb4_unicode_ci COMMENT '触发条件',
  `time_window` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '时间窗口',
  `sql_ext` text COLLATE utf8mb4_unicode_ci COMMENT 'sql语句',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建用户',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改用户',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  PRIMARY KEY (`id`,`task_id`,`rule_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_monitor_task
-- ----------------------------
DROP TABLE IF EXISTS `t_monitor_task`;
CREATE TABLE `t_monitor_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名字',
  `des` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `data_source_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据源id',
  `db_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据库',
  `db_table` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表',
  `job_id` bigint(20) NOT NULL COMMENT 'xxjob执行id',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'table->单表监控，define->定义监控',
  `group_id` bigint(20) NOT NULL,
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `table_count` int(11) DEFAULT NULL COMMENT '表数',
  `lock_status` tinyint(1) DEFAULT '0' COMMENT '是否锁定 0:未锁定  1:锁定',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  `status_` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`,`job_id`,`group_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='质量监控-任务表';

-- ----------------------------
-- Table structure for t_quality_dimension
-- ----------------------------
DROP TABLE IF EXISTS `t_quality_dimension`;
CREATE TABLE `t_quality_dimension` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for t_quality_group
-- ----------------------------
DROP TABLE IF EXISTS `t_quality_group`;
CREATE TABLE `t_quality_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分组名称',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `level` int(11) DEFAULT NULL COMMENT '层级',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '层级路径',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='质量模版-分组表';

-- ----------------------------
-- Table structure for t_quality_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_quality_rule`;
CREATE TABLE `t_quality_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agg` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '聚合函数',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'NORMAL->正常使用，DEL->无效',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'build->内置，define->自定义',
  `group_id` bigint(20) NOT NULL,
  `dimension_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分组ID',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `target` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'table,field',
  `sql_ext` text COLLATE utf8mb4_unicode_ci COMMENT '自定义模板sql',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `cite_count` int(11) DEFAULT '0',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='模板信息表';

-- ----------------------------
-- Table structure for t_task_record
-- ----------------------------
DROP TABLE IF EXISTS `t_task_record`;
CREATE TABLE `t_task_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务名称',
  `check_result` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'true,false',
  `execute_time` datetime DEFAULT NULL COMMENT '执行时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建用户',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改用户',
  `flag` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'now-最新任务，past-过期任务',
  `execute_date` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'yyyymmdd',
  `consume_time` double DEFAULT NULL COMMENT '耗时',
  `exception_alert` text COLLATE utf8mb4_unicode_ci COMMENT '异常执行结果',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_check_result` (`check_result`) USING BTREE,
  KEY `index_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=138025 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='数据质量-任务执行结果表';

-- ----------------------------
-- Table structure for t_task_record_rule
-- ----------------------------
DROP TABLE IF EXISTS `t_task_record_rule`;
CREATE TABLE `t_task_record_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_id` bigint(20) NOT NULL COMMENT '报告id',
  `strategy_log_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '策略id',
  `check_result` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '校验结果(true,false)',
  `exec_sql` text COLLATE utf8mb4_unicode_ci COMMENT '执行的sql语句',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行时间',
  `execute_time` datetime DEFAULT NULL COMMENT '执行时间',
  `execute_date` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'yyyymmdd',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改用户',
  `exception_alert` text COLLATE utf8mb4_unicode_ci COMMENT '异常',
  PRIMARY KEY (`id`,`record_id`) USING BTREE,
  KEY `index_record_id` (`record_id`)
) ENGINE=InnoDB AUTO_INCREMENT=261488 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='质量监控-针对每个任务的策略(任务-策略 1-N)结果';

-- ----------------------------
-- Table structure for t_task_record_rule_detail
-- ----------------------------
DROP TABLE IF EXISTS `t_task_record_rule_detail`;
CREATE TABLE `t_task_record_rule_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_rule_id` bigint(20) NOT NULL COMMENT '报告id',
  `field` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '字段',
  `check_result` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '校验结果(true,false)',
  `field_content` text COLLATE utf8mb4_unicode_ci COMMENT '字段执行结果',
  `create_user` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `check_condition` text COLLATE utf8mb4_unicode_ci COMMENT '检测条件',
  `exception_alert` text COLLATE utf8mb4_unicode_ci COMMENT '异常',
  PRIMARY KEY (`id`,`record_rule_id`) USING BTREE,
  KEY `index_record_rule_id` (`record_rule_id`)
) ENGINE=InnoDB AUTO_INCREMENT=754115 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='数据质量-每个任务下面的每个策略（满足于不满足的数据）';

-- ----------------------------
-- Table structure for t_task_report
-- ----------------------------
DROP TABLE IF EXISTS `t_task_report`;
CREATE TABLE `t_task_report` (
  `id` bigint(20) NOT NULL,
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `run_time` bigint(20) NOT NULL COMMENT '执行时间',
  `warning_count` int(11) DEFAULT NULL COMMENT '警告数量',
  `warning_ratio` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '警告比例',
  `db_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据库名称',
  `data_source_key` int(11) DEFAULT NULL COMMENT '数据库id(非必要参数，应为记录数据库是之前的数据库并非当前的数据可能数据信息会有误差)',
  `db_table` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据库表',
  PRIMARY KEY (`id`,`task_id`,`run_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='警告基本信息';

-- ----------------------------
-- Table structure for t_task_strategy_log
-- ----------------------------
DROP TABLE IF EXISTS `t_task_strategy_log`;
CREATE TABLE `t_task_strategy_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `strategy_id` bigint(20) NOT NULL COMMENT '监控规则ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `rule_id` bigint(20) NOT NULL COMMENT '规则id',
  `rule_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'table->单表监控，define->定义监控',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '策略名称',
  `des` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '策略描述',
  `fields` text COLLATE utf8mb4_unicode_ci COMMENT '监控字段',
  `aggregation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '聚合操作',
  `filters` longtext COLLATE utf8mb4_unicode_ci COMMENT '过滤条件',
  `triggers` longtext COLLATE utf8mb4_unicode_ci COMMENT '触发条件',
  `time_window` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '时间窗口',
  `sql_ext` text COLLATE utf8mb4_unicode_ci COMMENT 'sql语句',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建用户',
  `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改用户',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 默认值 0 未删除，1 已删除',
  PRIMARY KEY (`id`,`task_id`,`rule_id`,`strategy_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=261488 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='监控模版';

SET FOREIGN_KEY_CHECKS = 1;
