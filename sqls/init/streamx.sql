/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : streamx

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:33:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bas_audit
-- ----------------------------
DROP TABLE IF EXISTS `bas_audit`;
CREATE TABLE `bas_audit` (
  `audit_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0',
  `username` varchar(64) NOT NULL,
  `module_code` varchar(32) NOT NULL,
  `operation_type` varchar(32) NOT NULL,
  `descr` varchar(1024) DEFAULT NULL,
  `duration` bigint(20) DEFAULT NULL COMMENT 'Request duration',
  `operation_status` tinyint(4) DEFAULT NULL,
  `method` varchar(256) DEFAULT NULL,
  `parameters` text,
  `exception` text,
  `ip` varchar(256) DEFAULT NULL,
  `create_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`audit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for bas_catalog
-- ----------------------------
DROP TABLE IF EXISTS `bas_catalog`;
CREATE TABLE `bas_catalog` (
  `catalog_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `plugin_id` bigint(20) NOT NULL,
  `catalog_name` varchar(64) NOT NULL,
  `catalog_type` varchar(32) NOT NULL DEFAULT 'generic_in_memory',
  `default_database` varchar(64) NOT NULL,
  `descr` varchar(256) DEFAULT NULL,
  `properties` text,
  `default_catalog` tinyint(1) NOT NULL DEFAULT '0',
  `create_user_id` bigint(20) NOT NULL DEFAULT '0',
  `modify_user_id` bigint(20) NOT NULL DEFAULT '0',
  `create_time` bigint(20) NOT NULL,
  `modify_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`catalog_id`),
  KEY `catalog_name_idx` (`catalog_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for bas_function
-- ----------------------------
DROP TABLE IF EXISTS `bas_function`;
CREATE TABLE `bas_function` (
  `function_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `catalog_id` bigint(20) NOT NULL,
  `function_name` varchar(64) NOT NULL,
  `database` varchar(64) NOT NULL,
  `invocation` varchar(64) NOT NULL,
  `function_path` varchar(1024) NOT NULL,
  `class_name` varchar(64) NOT NULL,
  `descr` varchar(256) DEFAULT NULL,
  `create_user_id` bigint(20) NOT NULL DEFAULT '0',
  `modify_user_id` bigint(20) NOT NULL DEFAULT '0',
  `create_time` bigint(20) NOT NULL,
  `modify_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`function_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for bas_permission
-- ----------------------------
DROP TABLE IF EXISTS `bas_permission`;
CREATE TABLE `bas_permission` (
  `permission_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `permission_group` varchar(64) NOT NULL,
  `permission_name` varchar(64) NOT NULL,
  `permission_code` varchar(64) NOT NULL,
  `create_time` bigint(20) NOT NULL,
  `modify_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`permission_id`),
  KEY `permission_name_idx` (`permission_name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for bas_plugin
-- ----------------------------
DROP TABLE IF EXISTS `bas_plugin`;
CREATE TABLE `bas_plugin` (
  `plugin_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `plugin_name` varchar(64) NOT NULL,
  `plugin_code` varchar(64) NOT NULL,
  `descr` varchar(256) DEFAULT NULL,
  `default_plugin` tinyint(1) NOT NULL DEFAULT '0',
  `create_user_id` bigint(20) NOT NULL DEFAULT '0',
  `modify_user_id` bigint(20) NOT NULL DEFAULT '0',
  `create_time` bigint(20) NOT NULL,
  `modify_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`plugin_id`),
  KEY `plugin_name_idx` (`plugin_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for bas_table
-- ----------------------------
DROP TABLE IF EXISTS `bas_table`;
CREATE TABLE `bas_table` (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `catalog_id` bigint(20) NOT NULL,
  `database` varchar(64) NOT NULL,
  `table_name` varchar(128) NOT NULL,
  `ddl` text COMMENT 'Base64 encode',
  `descr` varchar(256) DEFAULT NULL,
  `create_user_id` bigint(20) NOT NULL DEFAULT '0',
  `modify_user_id` bigint(20) NOT NULL DEFAULT '0',
  `create_time` bigint(20) NOT NULL,
  `modify_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for bas_task
-- ----------------------------
DROP TABLE IF EXISTS `bas_task`;
CREATE TABLE `bas_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `catalog_id` bigint(20) NOT NULL,
  `task_name` varchar(64) NOT NULL,
  `descr` varchar(256) DEFAULT NULL,
  `database` varchar(64) NOT NULL,
  `task_source` longtext COMMENT 'Base64 encode',
  `task_status` tinyint(4) DEFAULT NULL,
  `task_log` text,
  `table_graph` text,
  `column_graph` text,
  `lineage_time` bigint(20) DEFAULT NULL,
  `create_user_id` bigint(20) NOT NULL DEFAULT '0',
  `modify_user_id` bigint(20) NOT NULL DEFAULT '0',
  `create_time` bigint(20) NOT NULL,
  `modify_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`task_id`),
  KEY `task_name_idx` (`task_name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dynamic_api
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_api`;
CREATE TABLE `dynamic_api` (
  `id` bigint(20) NOT NULL,
  `content` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `method` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `param` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `removed` bit(1) NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dynamic_function
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_function`;
CREATE TABLE `dynamic_function` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `is_removed` bit(1) NOT NULL,
  `mapping_path` varchar(255) DEFAULT NULL,
  `parameters` varchar(255) DEFAULT NULL,
  `return_type` varchar(255) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for rel_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `rel_role_permission`;
CREATE TABLE `rel_role_permission` (
  `rid` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL,
  `permission_id` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`rid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for rel_task_function
-- ----------------------------
DROP TABLE IF EXISTS `rel_task_function`;
CREATE TABLE `rel_task_function` (
  `rid` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_id` bigint(20) NOT NULL,
  `sql_id` bigint(20) NOT NULL,
  `function_id` bigint(20) NOT NULL,
  `catalog_name` varchar(64) NOT NULL,
  `database` varchar(64) NOT NULL,
  `function_name` varchar(64) NOT NULL,
  `create_time` bigint(20) NOT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for rel_task_lineage
-- ----------------------------
DROP TABLE IF EXISTS `rel_task_lineage`;
CREATE TABLE `rel_task_lineage` (
  `rid` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_id` bigint(20) NOT NULL,
  `sql_id` bigint(20) NOT NULL,
  `source_catalog` varchar(64) NOT NULL,
  `source_database` varchar(64) NOT NULL,
  `source_table` varchar(64) NOT NULL,
  `source_column` varchar(64) NOT NULL,
  `target_catalog` varchar(64) NOT NULL,
  `target_database` varchar(64) NOT NULL,
  `target_table` varchar(64) NOT NULL,
  `target_column` varchar(64) NOT NULL,
  `transform` varchar(256) DEFAULT NULL,
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`rid`),
  KEY `task_id_idx` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for rel_task_sql
-- ----------------------------
DROP TABLE IF EXISTS `rel_task_sql`;
CREATE TABLE `rel_task_sql` (
  `sql_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_id` bigint(20) NOT NULL,
  `sql_source` text COMMENT 'Base64 encode',
  `sql_type` varchar(16) NOT NULL,
  `start_line_number` bigint(20) DEFAULT NULL,
  `sql_status` tinyint(4) NOT NULL DEFAULT '0',
  `invalid` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`sql_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_alert_config
-- ----------------------------
DROP TABLE IF EXISTS `t_alert_config`;
CREATE TABLE `t_alert_config` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `alert_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'alert group name',
  `alert_type` int(11) DEFAULT '0' COMMENT 'alert type',
  `email_params` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'email params',
  `sms_params` text COLLATE utf8mb4_unicode_ci COMMENT 'sms params',
  `ding_talk_params` text COLLATE utf8mb4_unicode_ci COMMENT 'ding talk params',
  `we_com_params` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'wechat params',
  `http_callback_params` text COLLATE utf8mb4_unicode_ci COMMENT 'http callback params',
  `lark_params` text COLLATE utf8mb4_unicode_ci COMMENT 'lark params',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'change time',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_app_backup
-- ----------------------------
DROP TABLE IF EXISTS `t_app_backup`;
CREATE TABLE `t_app_backup` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `APP_ID` bigint(20) DEFAULT NULL COMMENT 'app任务的id',
  `SQL_ID` bigint(20) DEFAULT NULL COMMENT 'sql的sql',
  `CONFIG_ID` bigint(20) DEFAULT NULL COMMENT '配置文件id',
  `VERSION` int(11) DEFAULT NULL COMMENT '备份时的版本号',
  `PATH` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备份的位置',
  `DESCRIPTION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_app_build_pipe
-- ----------------------------
DROP TABLE IF EXISTS `t_app_build_pipe`;
CREATE TABLE `t_app_build_pipe` (
  `APP_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PIPE_TYPE` tinyint(4) DEFAULT NULL,
  `PIPE_STATUS` tinyint(4) DEFAULT NULL COMMENT '状态0,1,2,3,4,5',
  `CUR_STEP` smallint(6) DEFAULT NULL,
  `TOTAL_STEP` smallint(6) DEFAULT NULL,
  `STEPS_STATUS` text COLLATE utf8mb4_unicode_ci,
  `STEPS_STATUS_TS` text COLLATE utf8mb4_unicode_ci,
  `ERROR` text COLLATE utf8mb4_unicode_ci,
  `BUILD_RESULT` text COLLATE utf8mb4_unicode_ci,
  `MODIFY_TIME` datetime DEFAULT NULL,
  PRIMARY KEY (`APP_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=156 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_app_group
-- ----------------------------
DROP TABLE IF EXISTS `t_app_group`;
CREATE TABLE `t_app_group` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `GROUP_NAME` varchar(50) DEFAULT NULL COMMENT '分组名称',
  `DESCRIPTION` varchar(255) DEFAULT NULL COMMENT '分组描述',
  `PARENT_ID` bigint(20) DEFAULT NULL COMMENT '父分组id',
  `PARENT_NAME` varchar(100) DEFAULT NULL COMMENT '父类名称',
  `LEVEL_ID` bigint(20) DEFAULT NULL COMMENT '分类级别(一级：1；二级：2；三级：3)',
  `DELETES` bigint(20) DEFAULT NULL COMMENT '逻辑删除（0-未删除；1-已删除）',
  `CREATE_NAME` varchar(50) NOT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `CREATE_BY` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_app_hadoop
-- ----------------------------
DROP TABLE IF EXISTS `t_app_hadoop`;
CREATE TABLE `t_app_hadoop` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `HADOOP_NAME` varchar(255) DEFAULT NULL COMMENT 'hadoop集群名称',
  `HADOOP_ADDRESS` varchar(255) DEFAULT NULL COMMENT 'HADOOP集群地址',
  `DESCRIPTION` varchar(255) DEFAULT NULL COMMENT '描述',
  `HADOOP_PATH` varchar(100) DEFAULT NULL COMMENT 'XML文件路径',
  `CREATE_NAME` varchar(50) NOT NULL COMMENT '创建人',
  `DELETES` bigint(20) DEFAULT NULL COMMENT '逻辑删除（0-未删除；1-已删除）',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `CREATE_BY` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_external_link
-- ----------------------------
DROP TABLE IF EXISTS `t_external_link`;
CREATE TABLE `t_external_link` (
  `id` int(11) NOT NULL COMMENT 'key',
  `badge_label` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `badge_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `badge_color` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `link_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flame_graph
-- ----------------------------
DROP TABLE IF EXISTS `t_flame_graph`;
CREATE TABLE `t_flame_graph` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `APP_ID` bigint(20) DEFAULT NULL,
  `PROFILER` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `TIMELINE` datetime DEFAULT NULL,
  `CONTENT` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`ID`),
  KEY `INX_APPID` (`APP_ID`),
  KEY `INX_TIME` (`TIMELINE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_app
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_app`;
CREATE TABLE `t_flink_app` (
  `MODULE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用程序模块',
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `JOB_TYPE` tinyint(4) DEFAULT NULL COMMENT '任务类型 1:code 2:flink SQL',
  `EXECUTION_MODE` tinyint(4) DEFAULT NULL COMMENT '提交模式：0:local 1:remote 2:yarn-per-job 3:yarn-session 4:yarn-application 5:kubernetes-session 6:kubernetes-application',
  `RESOURCE_FROM` tinyint(1) DEFAULT NULL,
  `PROJECT_ID` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联任务',
  `JOB_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '前端和程序在yarn中显示的名称',
  `JAR` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `JAR_CHECK_SUM` bigint(20) DEFAULT NULL,
  `MAIN_CLASS` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ARGS` text COLLATE utf8mb4_unicode_ci,
  `OPTIONS` text COLLATE utf8mb4_unicode_ci,
  `HOT_PARAMS` text COLLATE utf8mb4_unicode_ci,
  `USER_ID` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `APP_ID` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'job编号',
  `APP_TYPE` tinyint(4) DEFAULT NULL COMMENT '任务类型',
  `DURATION` bigint(20) DEFAULT NULL COMMENT 'job唯一id',
  `JOB_ID` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `VERSION_ID` bigint(20) DEFAULT NULL,
  `CLUSTER_ID` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `K8S_NAMESPACE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `FLINK_IMAGE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `STATE` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `RESTART_SIZE` int(11) DEFAULT NULL,
  `RESTART_COUNT` int(11) DEFAULT NULL,
  `CP_THRESHOLD` int(11) DEFAULT NULL,
  `CP_MAX_FAILURE_INTERVAL` int(11) DEFAULT NULL,
  `CP_FAILURE_RATE_INTERVAL` int(11) DEFAULT NULL,
  `CP_FAILURE_ACTION` tinyint(4) DEFAULT NULL,
  `DYNAMIC_OPTIONS` text COLLATE utf8mb4_unicode_ci,
  `DESCRIPTION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `RESOLVE_ORDER` tinyint(4) DEFAULT NULL,
  `K8S_REST_EXPOSED_TYPE` tinyint(4) DEFAULT NULL,
  `FLAME_GRAPH` tinyint(4) DEFAULT '0',
  `JM_MEMORY` int(11) DEFAULT NULL,
  `TM_MEMORY` int(11) DEFAULT NULL,
  `TOTAL_TASK` int(11) DEFAULT NULL,
  `TOTAL_TM` int(11) DEFAULT NULL,
  `TOTAL_SLOT` int(11) DEFAULT NULL,
  `AVAILABLE_SLOT` int(11) DEFAULT NULL,
  `OPTION_STATE` tinyint(4) DEFAULT NULL,
  `TRACKING` tinyint(4) DEFAULT NULL,
  `CREATE_TIME` datetime DEFAULT NULL,
  `RELEASE` tinyint(4) DEFAULT '1' COMMENT '暂定任务是否需要回滚',
  `BUILD` tinyint(4) DEFAULT '1' COMMENT '是否构建 1:需要构建 0:不需要构建',
  `START_TIME` datetime DEFAULT NULL,
  `END_TIME` datetime DEFAULT NULL,
  `ALERT_ID` bigint(20) DEFAULT NULL,
  `K8S_POD_TEMPLATE` text COLLATE utf8mb4_unicode_ci,
  `K8S_JM_POD_TEMPLATE` text COLLATE utf8mb4_unicode_ci,
  `K8S_TM_POD_TEMPLATE` text COLLATE utf8mb4_unicode_ci,
  `K8S_HADOOP_INTEGRATION` tinyint(1) DEFAULT '0',
  `FLINK_CLUSTER_ID` bigint(20) DEFAULT NULL,
  `HADOOP_ID` bigint(20) DEFAULT NULL COMMENT 'HADOOP集群ID',
  `ONE_GROUP_ID` bigint(20) DEFAULT NULL COMMENT '一级分组ID',
  `TWO_GROUP_ID` bigint(20) DEFAULT NULL COMMENT '二级分组ID',
  `ONE_GROUP_NAME` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '一级分组名称',
  `TWO_GROUP_NAME` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二级分组名称',
  `HADOOP_ADDRESS` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'hadoop地址',
  `HADOOP_NAME` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '集群名称',
  `HADOOP_PATH` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '集群xml文件路径',
  `UPDATE_TIME` datetime DEFAULT NULL,
  `UPDATE_NAME` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `TEAM_ID` bigint(20) DEFAULT NULL,
  `JOB_MANAGER_URL` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `K8S_NAME` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `MODIFY_TIME` datetime DEFAULT NULL,
  `OPTION_TIME` datetime DEFAULT NULL,
  `INGRESS_TEMPLATE` text COLLATE utf8mb4_unicode_ci,
  `DEFAULT_MODE_INGRESS` text COLLATE utf8mb4_unicode_ci,
  `HADOOP_USER` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `TAGS` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `DYNAMIC_PROPERTIES` text COLLATE utf8mb4_unicode_ci,
  `dependency` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`ID`),
  KEY `INX_JOB_TYPE` (`JOB_TYPE`),
  KEY `INX_STATE` (`STATE`),
  KEY `INX_TRACK` (`TRACKING`)
) ENGINE=InnoDB AUTO_INCREMENT=157 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_cluster
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_cluster`;
CREATE TABLE `t_flink_cluster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `address` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'url address of cluster',
  `job_manager_url` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'url address of jobmanager',
  `cluster_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'clusterId of session mode(yarn-session:application-id,k8s-session:cluster-id)',
  `cluster_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'cluster name',
  `options` text COLLATE utf8mb4_unicode_ci COMMENT 'json form of parameter collection ',
  `yarn_queue` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'the yarn queue where the task is located',
  `execution_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'k8s execution session mode(1:remote,3:yarn-session,5:kubernetes-session)',
  `version_id` bigint(20) NOT NULL COMMENT 'flink version id',
  `k8s_namespace` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT 'default' COMMENT 'k8s namespace',
  `service_account` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'k8s service account',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `flink_image` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'flink image',
  `dynamic_properties` text COLLATE utf8mb4_unicode_ci COMMENT 'allows specifying multiple generic configuration options',
  `k8s_rest_exposed_type` tinyint(4) DEFAULT '2' COMMENT 'k8s export(0:loadbalancer,1:clusterip,2:nodeport)',
  `k8s_hadoop_integration` tinyint(4) DEFAULT '0',
  `k8s_conf` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'the path where the k 8 s configuration file is located',
  `resolve_order` tinyint(4) DEFAULT NULL,
  `exception` text COLLATE utf8mb4_unicode_ci COMMENT 'exception information',
  `cluster_state` tinyint(4) DEFAULT '0' COMMENT 'cluster status (0: created but not started, 1: started, 2: stopped)',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `start_time` datetime DEFAULT NULL COMMENT 'start time',
  `end_time` datetime DEFAULT NULL COMMENT 'end time',
  `alert_id` bigint(20) DEFAULT NULL COMMENT 'alert id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`,`cluster_name`),
  UNIQUE KEY `cluster_id` (`cluster_id`,`address`,`execution_mode`)
) ENGINE=InnoDB AUTO_INCREMENT=100001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_config
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_config`;
CREATE TABLE `t_flink_config` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `APP_ID` bigint(20) NOT NULL COMMENT '任务id',
  `FORMAT` tinyint(4) NOT NULL DEFAULT '0' COMMENT '格式化类型',
  `VERSION` int(11) NOT NULL COMMENT '版本',
  `LATEST` tinyint(4) NOT NULL DEFAULT '0' COMMENT '标记，运行标记',
  `CONTENT` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置文件',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`),
  KEY `index_appid` (`APP_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_effective
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_effective`;
CREATE TABLE `t_flink_effective` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `APP_ID` bigint(20) NOT NULL COMMENT 'app任务id',
  `TARGET_TYPE` tinyint(4) NOT NULL COMMENT '1) config 2) flink sql',
  `TARGET_ID` bigint(20) NOT NULL COMMENT 'configId or sqlId',
  `CREATE_TIME` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UN_INX` (`APP_ID`,`TARGET_TYPE`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_env
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_env`;
CREATE TABLE `t_flink_env` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `FLINK_NAME` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Flink实例名称',
  `FLINK_HOME` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Flink Home路径',
  `VERSION` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Flink对应的版本号',
  `SCALA_VERSION` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Flink对应的scala版本号',
  `FLINK_CONF` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'flink-conf配置内容',
  `IS_DEFAULT` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否为默认版本',
  `DESCRIPTION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述信息',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `DELETES` bigint(20) DEFAULT NULL COMMENT '是否删除0-未删除，1-已删除',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `CREATE_NAME` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `CREATE_BY` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UN_NAME` (`FLINK_NAME`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Flink version';

-- ----------------------------
-- Table structure for t_flink_log
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_log`;
CREATE TABLE `t_flink_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` bigint(20) DEFAULT NULL,
  `yarn_app_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `job_manager_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `success` tinyint(4) DEFAULT NULL,
  `exception` text COLLATE utf8mb4_unicode_ci,
  `option_time` datetime DEFAULT NULL,
  `option_name` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index_appid` (`app_id`)
) ENGINE=InnoDB AUTO_INCREMENT=608 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_project
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_project`;
CREATE TABLE `t_flink_project` (
  `id` bigint(20) NOT NULL,
  `team_id` bigint(20) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `branches` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `salt` varchar(26) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `prvkey_path` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pom` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `build_args` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` tinyint(4) DEFAULT NULL,
  `repository` tinyint(4) DEFAULT NULL,
  `last_build` datetime DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `build_state` tinyint(4) DEFAULT '-1',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_savepoint
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_savepoint`;
CREATE TABLE `t_flink_savepoint` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `CHK_ID` bigint(20) DEFAULT NULL,
  `APP_ID` bigint(20) NOT NULL,
  `TYPE` tinyint(4) DEFAULT NULL,
  `PATH` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `LATEST` tinyint(4) NOT NULL,
  `TRIGGER_TIME` datetime DEFAULT NULL,
  `CREATE_TIME` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1041 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_sql
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_sql`;
CREATE TABLE `t_flink_sql` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `APP_ID` bigint(20) DEFAULT NULL COMMENT '任务id',
  `SQL` text COLLATE utf8mb4_unicode_ci COMMENT '加密sql',
  `DEPENDENCY` text COLLATE utf8mb4_unicode_ci COMMENT '第三方依赖',
  `VERSION` int(11) DEFAULT NULL COMMENT '版本',
  `CANDIDATE` tinyint(4) NOT NULL DEFAULT '1' COMMENT '候选版本:0: 非候选(默认) 1:新增的记录成为候选版本 2:指定历史记录的版本成为候选版本',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `team_resource` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `index_app_id` (`APP_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=352 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_sql_copy1
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_sql_copy1`;
CREATE TABLE `t_flink_sql_copy1` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `APP_ID` bigint(20) DEFAULT NULL COMMENT '任务id',
  `SQL` text COLLATE utf8mb4_unicode_ci COMMENT '加密sql',
  `DEPENDENCY` text COLLATE utf8mb4_unicode_ci COMMENT '第三方依赖',
  `VERSION` int(11) DEFAULT NULL COMMENT '版本',
  `CANDIDATE` tinyint(4) NOT NULL DEFAULT '1' COMMENT '候选版本:0: 非候选(默认) 1:新增的记录成为候选版本 2:指定历史记录的版本成为候选版本',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `team_resource` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `index_appid` (`APP_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=275 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_flink_tutorial
-- ----------------------------
DROP TABLE IF EXISTS `t_flink_tutorial`;
CREATE TABLE `t_flink_tutorial` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `TYPE` tinyint(4) DEFAULT NULL,
  `NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `CONTENT` text COLLATE utf8mb4_unicode_ci,
  `CREATE_TIME` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_member
-- ----------------------------
DROP TABLE IF EXISTS `t_member`;
CREATE TABLE `t_member` (
  `id` bigint(20) NOT NULL,
  `team_id` bigint(20) NOT NULL COMMENT 'team id',
  `user_id` bigint(20) NOT NULL COMMENT 'user id',
  `role_id` bigint(20) NOT NULL COMMENT 'role id',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_id` (`team_id`,`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_menu`;
CREATE TABLE `t_menu` (
  `MENU_ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单/按钮ID',
  `PARENT_ID` bigint(20) NOT NULL COMMENT '上级菜单ID',
  `MENU_NAME` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单/按钮名称',
  `PATH` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对应路由path',
  `COMPONENT` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对应路由组件component',
  `PERMS` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限标识',
  `ICON` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `TYPE` char(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型 0菜单 1按钮',
  `DISPLAY` char(2) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '菜单是否显示',
  `ORDER_NUM` double(20,0) DEFAULT NULL COMMENT '排序',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`MENU_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=150505 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_message
-- ----------------------------
DROP TABLE IF EXISTS `t_message`;
CREATE TABLE `t_message` (
  `id` bigint(20) NOT NULL,
  `app_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `type` tinyint(4) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `context` text COLLATE utf8mb4_unicode_ci,
  `is_read` tinyint(4) DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_resource
-- ----------------------------
DROP TABLE IF EXISTS `t_resource`;
CREATE TABLE `t_resource` (
  `id` bigint(20) NOT NULL,
  `resource_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'The name of the resource file',
  `resource_type` int(11) NOT NULL COMMENT '0:app 1:common 2:connector 3:format 4:udf',
  `resource_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resource` text COLLATE utf8mb4_unicode_ci,
  `engine_type` int(11) NOT NULL COMMENT 'compute engine type, 0:apache flink 1:apache spark',
  `main_class` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'More detailed description of resource',
  `creator_id` bigint(20) NOT NULL COMMENT 'user id of creator',
  `connector_required_options` text COLLATE utf8mb4_unicode_ci,
  `connector_optional_options` text COLLATE utf8mb4_unicode_ci,
  `team_id` bigint(20) NOT NULL COMMENT 'team id',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_role
-- ----------------------------
DROP TABLE IF EXISTS `t_role`;
CREATE TABLE `t_role` (
  `role_id` bigint(20) NOT NULL COMMENT 'user id',
  `role_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'user name',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'description',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_role_menu`;
CREATE TABLE `t_role_menu` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ROLE_ID` bigint(20) NOT NULL,
  `MENU_ID` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UN_INX` (`ROLE_ID`,`MENU_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=100111 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_setting
-- ----------------------------
DROP TABLE IF EXISTS `t_setting`;
CREATE TABLE `t_setting` (
  `ORDER_NUM` int(11) DEFAULT NULL,
  `SETTING_KEY` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `SETTING_VALUE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `SETTING_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `DESCRIPTION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `TYPE` tinyint(4) NOT NULL COMMENT '1: input 2: boolean 3: number',
  PRIMARY KEY (`SETTING_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_spark_app
-- ----------------------------
DROP TABLE IF EXISTS `t_spark_app`;
CREATE TABLE `t_spark_app` (
  `id` bigint(20) NOT NULL,
  `team_id` bigint(20) NOT NULL,
  `job_type` tinyint(4) DEFAULT NULL,
  `execution_mode` tinyint(4) DEFAULT NULL,
  `resource_from` tinyint(4) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `job_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `module` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `jar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `jar_check_sum` bigint(20) DEFAULT NULL,
  `main_class` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `args` text COLLATE utf8mb4_unicode_ci,
  `options` text COLLATE utf8mb4_unicode_ci,
  `hot_params` text COLLATE utf8mb4_unicode_ci,
  `user_id` bigint(20) DEFAULT NULL,
  `app_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `app_type` tinyint(4) DEFAULT NULL,
  `duration` bigint(20) DEFAULT NULL,
  `job_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `job_manager_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version_id` bigint(20) DEFAULT NULL,
  `cluster_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `k8s_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `k8s_namespace` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `spark_image` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` int(11) DEFAULT NULL,
  `restart_size` int(11) DEFAULT NULL,
  `restart_count` int(11) DEFAULT NULL,
  `cp_threshold` int(11) DEFAULT NULL,
  `cp_max_failure_interval` int(11) DEFAULT NULL,
  `cp_failure_rate_interval` int(11) DEFAULT NULL,
  `cp_failure_action` tinyint(4) DEFAULT NULL,
  `dynamic_properties` text COLLATE utf8mb4_unicode_ci,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resolve_order` tinyint(4) DEFAULT NULL,
  `k8s_rest_exposed_type` tinyint(4) DEFAULT NULL,
  `jm_memory` int(11) DEFAULT NULL,
  `tm_memory` int(11) DEFAULT NULL,
  `total_task` int(11) DEFAULT NULL,
  `total_tm` int(11) DEFAULT NULL,
  `total_slot` int(11) DEFAULT NULL,
  `available_slot` int(11) DEFAULT NULL,
  `option_state` tinyint(4) DEFAULT NULL,
  `tracking` tinyint(4) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'modify time',
  `option_time` datetime DEFAULT NULL,
  `release` tinyint(4) DEFAULT '1',
  `build` tinyint(4) DEFAULT '1',
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `alert_id` bigint(20) DEFAULT NULL,
  `k8s_pod_template` text COLLATE utf8mb4_unicode_ci,
  `k8s_jm_pod_template` text COLLATE utf8mb4_unicode_ci,
  `k8s_tm_pod_template` text COLLATE utf8mb4_unicode_ci,
  `k8s_hadoop_integration` tinyint(4) DEFAULT '0',
  `spark_cluster_id` bigint(20) DEFAULT NULL,
  `ingress_template` text COLLATE utf8mb4_unicode_ci,
  `default_mode_ingress` text COLLATE utf8mb4_unicode_ci,
  `tags` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hadoop_user` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_spark_env
-- ----------------------------
DROP TABLE IF EXISTS `t_spark_env`;
CREATE TABLE `t_spark_env` (
  `id` bigint(20) NOT NULL,
  `spark_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'spark instance name',
  `spark_home` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'spark home path',
  `version` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'spark version',
  `scala_version` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'scala version of spark',
  `spark_conf` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'spark-conf',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'whether default version or not',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'description',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `spark_name` (`spark_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_team
-- ----------------------------
DROP TABLE IF EXISTS `t_team`;
CREATE TABLE `t_team` (
  `id` bigint(20) NOT NULL COMMENT 'team id',
  `team_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'team name',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_name` (`team_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user` (
  `user_id` bigint(20) NOT NULL COMMENT 'user id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'user name',
  `nick_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'nick name',
  `salt` varchar(26) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'salt',
  `password` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'password',
  `email` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'email',
  `user_type` int(11) NOT NULL COMMENT 'user type 1:admin 2:user',
  `login_type` tinyint(4) DEFAULT '0' COMMENT 'login type 0:password 1:ldap 2:sso',
  `last_team_id` bigint(20) DEFAULT NULL COMMENT 'last team id',
  `status` char(1) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'status 0:locked 1:active',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  `last_login_time` datetime DEFAULT NULL COMMENT 'last login time',
  `sex` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'gender 0:male 1:female 2:confidential',
  `avatar` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'avatar',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'description',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_user_role
-- ----------------------------
DROP TABLE IF EXISTS `t_user_role`;
CREATE TABLE `t_user_role` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `USER_ID` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `ROLE_ID` bigint(20) DEFAULT NULL COMMENT '角色ID',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UN_INX` (`USER_ID`,`ROLE_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_variable
-- ----------------------------
DROP TABLE IF EXISTS `t_variable`;
CREATE TABLE `t_variable` (
  `id` bigint(20) NOT NULL COMMENT 'variable id',
  `variable_code` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Variable code is used for parameter names passed to the program or as placeholders',
  `variable_value` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'The specific value corresponding to the variable',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'More detailed description of variables, only for display, not involved in program logic',
  `creator_id` bigint(20) NOT NULL COMMENT 'user id of creator',
  `team_id` bigint(20) NOT NULL COMMENT 'team id',
  `desensitization` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 is no desensitization, 1 is desensitization, if set to desensitization, it will be replaced by * when displayed',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_id` (`team_id`,`variable_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for t_yarn_queue
-- ----------------------------
DROP TABLE IF EXISTS `t_yarn_queue`;
CREATE TABLE `t_yarn_queue` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'queue id',
  `team_id` bigint(20) NOT NULL COMMENT 'team id',
  `queue_label` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'queue label expression',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'description of the queue label',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `modify_time` datetime DEFAULT NULL COMMENT 'modify time',
  `hadoop_id` bigint(20) DEFAULT NULL,
  `hadoop_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_by` bigint(20) DEFAULT NULL,
  `create_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_id` (`team_id`,`queue_label`,`hadoop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SET FOREIGN_KEY_CHECKS = 1;
