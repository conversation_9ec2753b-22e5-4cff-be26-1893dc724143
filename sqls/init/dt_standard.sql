/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_standard

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:33:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_dashboard
-- ----------------------------
DROP TABLE IF EXISTS `t_dashboard`;
CREATE TABLE `t_dashboard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `panel_name` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '大屏名称',
  `remark` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组id',
  `content` longtext COLLATE utf8mb4_bin COMMENT '图表信息',
  `url` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'url',
  `internal_path` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '内部路径',
  `deployed` tinyint(1) DEFAULT NULL COMMENT '是否以及发布',
  `create_time` date DEFAULT NULL COMMENT '创建时间',
  `update_time` date DEFAULT NULL COMMENT '修改时间',
  `creator` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新者',
  `mounted` tinyint(1) DEFAULT '0' COMMENT '是否挂载',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_dashboard_group
-- ----------------------------
DROP TABLE IF EXISTS `t_dashboard_group`;
CREATE TABLE `t_dashboard_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID',
  `name` varchar(255) DEFAULT NULL COMMENT '分组名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='大屏分组表';

-- ----------------------------
-- Table structure for t_folder
-- ----------------------------
DROP TABLE IF EXISTS `t_folder`;
CREATE TABLE `t_folder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID',
  `folder_name` varchar(255) DEFAULT NULL COMMENT '目录名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='目录表';

-- ----------------------------
-- Table structure for t_group
-- ----------------------------
DROP TABLE IF EXISTS `t_group`;
CREATE TABLE `t_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID',
  `name` varchar(255) DEFAULT NULL COMMENT '分组名称',
  `group_type` varchar(20) NOT NULL COMMENT '分组类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8mb4 COMMENT='标准分组表';

-- ----------------------------
-- Table structure for t_report
-- ----------------------------
DROP TABLE IF EXISTS `t_report`;
CREATE TABLE `t_report` (
  `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组id',
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `content` longtext COLLATE utf8mb4_bin COMMENT '内容',
  `lock_flag` tinyint(1) DEFAULT NULL COMMENT '是否锁定',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `creator` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'url',
  `http_method` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'http请求方法类型',
  `deployed` tinyint(1) DEFAULT '0' COMMENT '是否发布',
  `mounted` tinyint(1) DEFAULT '0' COMMENT '是否挂载',
  `version_num` int(11) DEFAULT NULL,
  `data_file_id` bigint(20) DEFAULT NULL,
  `excel_file_id` bigint(20) DEFAULT NULL,
  `parquet_file_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_standard
-- ----------------------------
DROP TABLE IF EXISTS `t_standard`;
CREATE TABLE `t_standard` (
  `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '名称',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组id',
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tag` int(10) DEFAULT NULL COMMENT '标签',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `content` longtext COLLATE utf8mb4_bin COMMENT '内容',
  `standard_type` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类型',
  `compose_type` tinyint(1) DEFAULT NULL COMMENT '是否组合类型',
  `lock_flag` tinyint(1) DEFAULT NULL COMMENT '是否锁定',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `creator` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者',
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'url',
  `http_method` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'http请求方法类型',
  `deployed` tinyint(1) DEFAULT '0' COMMENT '是否发布',
  `mounted` tinyint(1) DEFAULT '0' COMMENT '是否挂载',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Table structure for t_standard_group
-- ----------------------------
DROP TABLE IF EXISTS `t_standard_group`;
CREATE TABLE `t_standard_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID',
  `name` varchar(255) DEFAULT NULL COMMENT '分组名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='标准分组表';

SET FOREIGN_KEY_CHECKS = 1;
