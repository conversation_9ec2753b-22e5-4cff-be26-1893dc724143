/*
 Navicat Premium Dump SQL

 Source Server         : 大渡河-test
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 Source Host           : *************:3306
 Source Schema         : dt_licence

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-0ubuntu0.18.04.1-log)
 File Encoding         : 65001

 Date: 08/08/2025 10:32:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_licence
-- ----------------------------
DROP TABLE IF EXISTS `t_licence`;
CREATE TABLE `t_licence` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `expiry_time` datetime NOT NULL COMMENT '过期时间',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '描述',
  `extra_info` text COLLATE utf8mb4_bin COMMENT '额外信息',
  `uuid` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT 'uuid',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `company` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司',
  `dept` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

SET FOREIGN_KEY_CHECKS = 1;
