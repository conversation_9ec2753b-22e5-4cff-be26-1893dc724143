

-- bdp-data-quality

INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (1, 'LINE', '', '表行数', 'NORMAL', 'build', 5, 'table', '计算数据行数', 'table', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (2, 'AVG', 'AVG', '平均值', 'NORMAL', 'build', 5, 'exact', '计算数值平均值', 'field', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (3, 'SUM', 'SUM', '汇总值', 'NORMAL', 'build', 5, 'exact', '计算数值汇总值', 'field', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (5, 'MIN', 'MIN', '最小值', 'NORMAL', 'build', 5, 'exact', '计算数值最小值', 'field', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (6, 'MAX', 'MAX', '最大值', 'NORMAL', 'build', 5, 'exact', '计算数值最大值', 'field', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (7, 'RANGE', NULL, '数值范围', 'NORMAL', 'build', 5, 'uniformity', '数值范围', 'field', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (8, 'ENUM', NULL, '枚举范围', 'NORMAL', 'build', 5, 'uniformity', '枚举范围', 'field', NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (9, NULL, NULL, '模版007', 'NORMAL', 'define', 7, NULL, '电力处理', NULL, 'select * from user', 'chenzhongqin', '2025-04-14 17:25:12', 0, 0);
INSERT INTO `t_quality_rule` (`id`, `code`, `agg`, `name`, `status`, `type`, `group_id`, `dimension_code`, `description`, `target`, `sql_ext`, `create_user`, `create_time`, `cite_count`, `deleted`) VALUES (10, NULL, NULL, '测试模版1', 'NORMAL', 'define', 11, NULL, '测试模版1', NULL, 'select * from user', 'tian', '2025-08-01 10:35:53', 0, 0);



------------------------------------------  ------------------------------------------  ------------------------------------------
------------------------------------------      git tag 20250807                        ------------------------------------------
------------------------------------------  ------------------------------------------  ------------------------------------------


